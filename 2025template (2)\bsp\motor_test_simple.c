/**
 * @file motor_test_simple.c
 * @brief 简化版电机测试代码
 * @details 提供简单的电机测试函数，可以手动调用
 */

#include "bsp_system.h"
#include "step_motor_bsp.h"

/**
 * @brief 简单的电机测试函数
 * @details 按顺序执行电机测试：X正->Y正->X负->Y负->停止
 * @param 无
 * @retval 无
 */
void simple_motor_test(void)
{
    my_printf(&huart1, "开始电机测试...\r\n");
    
    // 1. X轴正方向运动 3秒
    my_printf(&huart1, "阶段1: X轴正方向 (10, 0)\r\n");
    Step_Motor_Set_Speed_my(10, 0);
    HAL_Delay(3000);
    
    // 2. Y轴正方向运动 3秒
    my_printf(&huart1, "阶段2: Y轴正方向 (0, 10)\r\n");
    Step_Motor_Set_Speed_my(0, 10);
    HAL_Delay(3000);
    
    // 3. X轴负方向运动 3秒
    my_printf(&huart1, "阶段3: X轴负方向 (-10, 0)\r\n");
    Step_Motor_Set_Speed_my(-10, 0);
    H<PERSON>_Delay(3000);
    
    // 4. Y轴负方向运动 3秒
    my_printf(&huart1, "阶段4: Y轴负方向 (0, -10)\r\n");
    Step_Motor_Set_Speed_my(0, -10);
    HAL_Delay(3000);
    
    // 5. 停止运动
    my_printf(&huart1, "阶段5: 停止运动 (0, 0)\r\n");
    Step_Motor_Set_Speed_my(0, 0);
    
    my_printf(&huart1, "电机测试完成！\r\n");
}

/**
 * @brief 非阻塞式电机测试函数
 * @details 使用状态机实现非阻塞测试，需要定期调用
 * @param 无
 * @retval 测试是否完成 (1=完成, 0=进行中)
 */
uint8_t non_blocking_motor_test(void)
{
    static uint8_t test_state = 0;
    static uint32_t state_start_time = 0;
    static uint8_t test_started = 0;
    uint32_t current_time = HAL_GetTick();
    const uint32_t STATE_DURATION = 3000; // 3秒
    
    // 初始化测试
    if (!test_started) {
        test_started = 1;
        test_state = 1;
        state_start_time = current_time;
        my_printf(&huart1, "开始非阻塞电机测试...\r\n");
        return 0;
    }
    
    // 状态机
    switch (test_state) {
        case 1: // X轴正方向
            Step_Motor_Set_Speed_my(10, 0);
            if (current_time - state_start_time >= STATE_DURATION) {
                test_state = 2;
                state_start_time = current_time;
                my_printf(&huart1, "切换到Y轴正方向\r\n");
            }
            break;
            
        case 2: // Y轴正方向
            Step_Motor_Set_Speed_my(0, 10);
            if (current_time - state_start_time >= STATE_DURATION) {
                test_state = 3;
                state_start_time = current_time;
                my_printf(&huart1, "切换到X轴负方向\r\n");
            }
            break;
            
        case 3: // X轴负方向
            Step_Motor_Set_Speed_my(-10, 0);
            if (current_time - state_start_time >= STATE_DURATION) {
                test_state = 4;
                state_start_time = current_time;
                my_printf(&huart1, "切换到Y轴负方向\r\n");
            }
            break;
            
        case 4: // Y轴负方向
            Step_Motor_Set_Speed_my(0, -10);
            if (current_time - state_start_time >= STATE_DURATION) {
                test_state = 5;
                state_start_time = current_time;
                my_printf(&huart1, "停止运动\r\n");
            }
            break;
            
        case 5: // 停止
            Step_Motor_Set_Speed_my(0, 0);
            my_printf(&huart1, "非阻塞电机测试完成！\r\n");
            test_started = 0; // 重置测试状态
            return 1; // 测试完成
            
        default:
            test_state = 0;
            test_started = 0;
            break;
    }
    
    return 0; // 测试进行中
}
